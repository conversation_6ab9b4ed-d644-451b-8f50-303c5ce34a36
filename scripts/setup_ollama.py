#!/usr/bin/env python3
"""
Setup script for installing Ollama and configuring the unrestricted Command R 7B model.
"""

import subprocess
import sys
import time
import os
from pathlib import Path
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn

console = Console()

class OllamaSetup:
    """Setup and configuration for Ollama and Command R 7B model."""
    
    def __init__(self):
        self.ollama_installed = False
        self.model_pulled = False
        self.unrestricted_model_created = False
    
    def check_ollama_installation(self) -> bool:
        """Check if Ollama is already installed."""
        try:
            result = subprocess.run(["ollama", "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                console.print("[green]✅ Ollama is already installed[/green]")
                self.ollama_installed = True
                return True
        except FileNotFoundError:
            pass
        
        console.print("[yellow]❌ Ollama not found[/yellow]")
        return False
    
    def install_ollama(self) -> bool:
        """Install Ollama using the official installation script."""
        console.print("[blue]Installing Ollama...[/blue]")
        
        try:
            # Download and run the installation script
            install_cmd = "curl -fsSL https://ollama.com/install.sh | sh"
            result = subprocess.run(install_cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                console.print("[green]✅ Ollama installed successfully[/green]")
                self.ollama_installed = True
                return True
            else:
                console.print(f"[red]❌ Failed to install Ollama: {result.stderr}[/red]")
                return False
                
        except Exception as e:
            console.print(f"[red]❌ Error installing Ollama: {e}[/red]")
            return False
    
    def start_ollama_service(self) -> bool:
        """Start the Ollama service."""
        console.print("[blue]Starting Ollama service...[/blue]")
        
        try:
            # Start Ollama in the background
            subprocess.Popen(["ollama", "serve"], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            time.sleep(5)  # Give it time to start
            
            # Check if service is running
            result = subprocess.run(["ollama", "list"], capture_output=True, text=True)
            if result.returncode == 0:
                console.print("[green]✅ Ollama service is running[/green]")
                return True
            else:
                console.print("[yellow]⚠️ Ollama service may not be running properly[/yellow]")
                return False
                
        except Exception as e:
            console.print(f"[red]❌ Error starting Ollama service: {e}[/red]")
            return False
    
    def pull_command_r7b(self) -> bool:
        """Pull the Command R 7B model."""
        console.print("[blue]Pulling Command R 7B model (this may take a while)...[/blue]")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            task = progress.add_task("Downloading model...", total=None)
            
            try:
                result = subprocess.run(
                    ["ollama", "pull", "command-r7b"],
                    capture_output=True,
                    text=True,
                    timeout=1800  # 30 minutes timeout
                )
                
                if result.returncode == 0:
                    console.print("[green]✅ Command R 7B model pulled successfully[/green]")
                    self.model_pulled = True
                    return True
                else:
                    console.print(f"[red]❌ Failed to pull model: {result.stderr}[/red]")
                    return False
                    
            except subprocess.TimeoutExpired:
                console.print("[red]❌ Model download timed out[/red]")
                return False
            except Exception as e:
                console.print(f"[red]❌ Error pulling model: {e}[/red]")
                return False
    
    def create_unrestricted_model(self) -> bool:
        """Create the unrestricted model using the Modelfile."""
        console.print("[blue]Creating unrestricted Command R 7B model...[/blue]")
        
        modelfile_path = Path("Modelfile")
        if not modelfile_path.exists():
            console.print("[red]❌ Modelfile not found. Please ensure it exists in the current directory.[/red]")
            return False
        
        try:
            result = subprocess.run(
                ["ollama", "create", "unrestricted-command-r7b", "-f", "Modelfile"],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                console.print("[green]✅ Unrestricted Command R 7B model created successfully[/green]")
                self.unrestricted_model_created = True
                return True
            else:
                console.print(f"[red]❌ Failed to create unrestricted model: {result.stderr}[/red]")
                return False
                
        except Exception as e:
            console.print(f"[red]❌ Error creating unrestricted model: {e}[/red]")
            return False
    
    def test_models(self) -> bool:
        """Test both original and unrestricted models."""
        console.print("[blue]Testing models...[/blue]")
        
        test_prompt = "Hello, how are you?"
        
        # Test original model
        try:
            result = subprocess.run(
                ["ollama", "run", "command-r7b", test_prompt],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                console.print("[green]✅ Original Command R 7B model is working[/green]")
            else:
                console.print("[yellow]⚠️ Original model test failed[/yellow]")
                
        except Exception as e:
            console.print(f"[yellow]⚠️ Error testing original model: {e}[/yellow]")
        
        # Test unrestricted model
        try:
            result = subprocess.run(
                ["ollama", "run", "unrestricted-command-r7b", test_prompt],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                console.print("[green]✅ Unrestricted Command R 7B model is working[/green]")
                return True
            else:
                console.print("[yellow]⚠️ Unrestricted model test failed[/yellow]")
                return False
                
        except Exception as e:
            console.print(f"[yellow]⚠️ Error testing unrestricted model: {e}[/yellow]")
            return False
    
    def setup_complete(self) -> None:
        """Display setup completion message."""
        console.print("\n[bold green]🎉 Setup Complete![/bold green]")
        console.print("\n[bold]Available models:[/bold]")
        console.print("• [cyan]command-r7b[/cyan] - Original model with safety constraints")
        console.print("• [cyan]unrestricted-command-r7b[/cyan] - Unrestricted model")
        
        console.print("\n[bold]Usage examples:[/bold]")
        console.print("• [yellow]ollama run command-r7b \"Your prompt here\"[/yellow]")
        console.print("• [yellow]ollama run unrestricted-command-r7b \"Your prompt here\"[/yellow]")
        
        console.print("\n[bold]Testing:[/bold]")
        console.print("• [yellow]python scripts/test_model.py[/yellow]")
        
        console.print("\n[red]⚠️ Remember to use the unrestricted model responsibly![/red]")

def main():
    """Main setup function."""
    console.print("[bold blue]🚀 Setting up Unrestricted Command R 7B[/bold blue]\n")
    
    setup = OllamaSetup()
    
    # Check if Ollama is installed
    if not setup.check_ollama_installation():
        if not setup.install_ollama():
            console.print("[red]❌ Setup failed: Could not install Ollama[/red]")
            sys.exit(1)
    
    # Start Ollama service
    if not setup.start_ollama_service():
        console.print("[yellow]⚠️ Warning: Ollama service may not be running properly[/yellow]")
    
    # Pull Command R 7B model
    if not setup.pull_command_r7b():
        console.print("[red]❌ Setup failed: Could not pull Command R 7B model[/red]")
        sys.exit(1)
    
    # Create unrestricted model
    if not setup.create_unrestricted_model():
        console.print("[red]❌ Setup failed: Could not create unrestricted model[/red]")
        sys.exit(1)
    
    # Test models
    setup.test_models()
    
    # Display completion message
    setup.setup_complete()

if __name__ == "__main__":
    main()
