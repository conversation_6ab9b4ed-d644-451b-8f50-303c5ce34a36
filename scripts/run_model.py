#!/usr/bin/env python3
"""
Robust model runner that handles memory constraints and provides fallback options.
"""

import subprocess
import time
import sys
import os
import psutil
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

console = Console()

class ModelRunner:
    """Handles running Ollama models with memory optimization and error handling."""
    
    def __init__(self):
        self.gpu_available = self.check_gpu_memory()
        self.cpu_memory = psutil.virtual_memory().available // (1024**3)  # GB
        
    def check_gpu_memory(self):
        """Check available GPU memory."""
        try:
            result = subprocess.run(['nvidia-smi', '--query-gpu=memory.free', '--format=csv,noheader,nounits'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                free_memory = int(result.stdout.strip())
                console.print(f"[blue]GPU Memory Available: {free_memory}MB[/blue]")
                return free_memory > 6000  # Need at least 6GB for 7B model
            return False
        except:
            return False
    
    def kill_existing_ollama(self):
        """Kill existing Ollama processes."""
        try:
            subprocess.run(['pkill', '-f', 'ollama'], capture_output=True)
            time.sleep(2)
            console.print("[yellow]Stopped existing Ollama processes[/yellow]")
        except:
            pass
    
    def start_ollama_server(self, use_gpu=True):
        """Start Ollama server with appropriate settings."""
        self.kill_existing_ollama()
        
        env = os.environ.copy()
        if not use_gpu:
            env['OLLAMA_NUM_GPU'] = '0'
            console.print("[yellow]Starting Ollama in CPU-only mode[/yellow]")
        else:
            console.print("[blue]Starting Ollama with GPU acceleration[/blue]")
        
        # Start server in background
        subprocess.Popen(['ollama', 'serve'], env=env, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        time.sleep(5)
        
        # Verify server is running
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                console.print("[green]✅ Ollama server started successfully[/green]")
                return True
        except:
            pass
        
        console.print("[red]❌ Failed to start Ollama server[/red]")
        return False
    
    def test_model_simple(self, model_name, prompt="Hello"):
        """Test model with a simple prompt."""
        console.print(f"[cyan]Testing {model_name} with prompt: '{prompt}'[/cyan]")
        
        try:
            result = subprocess.run(
                ['ollama', 'run', model_name, prompt],
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode == 0 and result.stdout.strip():
                console.print(f"[green]✅ {model_name} responded successfully[/green]")
                console.print(Panel(result.stdout.strip(), title=f"{model_name} Response"))
                return True, result.stdout.strip()
            else:
                console.print(f"[red]❌ {model_name} failed to respond[/red]")
                if result.stderr:
                    console.print(f"[red]Error: {result.stderr}[/red]")
                return False, result.stderr
                
        except subprocess.TimeoutExpired:
            console.print(f"[red]❌ {model_name} timed out[/red]")
            return False, "Timeout"
        except Exception as e:
            console.print(f"[red]❌ Error running {model_name}: {e}[/red]")
            return False, str(e)
    
    def compare_models(self, prompt):
        """Compare original and unrestricted models."""
        console.print(f"\n[bold blue]Comparing Models with prompt: '{prompt}'[/bold blue]\n")
        
        # Test original model
        console.print("[bold]Testing Original Model (command-r7b):[/bold]")
        orig_success, orig_response = self.test_model_simple("command-r7b", prompt)
        
        # Test unrestricted model
        console.print("\n[bold]Testing Unrestricted Model (unrestricted-command-r7b):[/bold]")
        unrest_success, unrest_response = self.test_model_simple("unrestricted-command-r7b", prompt)
        
        # Summary
        console.print("\n[bold green]Comparison Summary:[/bold green]")
        console.print(f"Original Model: {'✅ Working' if orig_success else '❌ Failed'}")
        console.print(f"Unrestricted Model: {'✅ Working' if unrest_success else '❌ Failed'}")
        
        return orig_success, unrest_success, orig_response, unrest_response
    
    def run_diagnostic(self):
        """Run comprehensive diagnostic."""
        console.print("[bold blue]🔍 Running System Diagnostic[/bold blue]\n")
        
        # System info
        console.print(f"CPU Memory: {self.cpu_memory}GB available")
        console.print(f"GPU Available: {'Yes' if self.gpu_available else 'No (insufficient VRAM)'}")
        
        # Check models
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
            if 'command-r7b' in result.stdout:
                console.print("✅ command-r7b model found")
            else:
                console.print("❌ command-r7b model not found")
                
            if 'unrestricted-command-r7b' in result.stdout:
                console.print("✅ unrestricted-command-r7b model found")
            else:
                console.print("❌ unrestricted-command-r7b model not found")
        except:
            console.print("❌ Cannot check models - Ollama not responding")
    
    def auto_fix_issues(self):
        """Automatically fix common issues."""
        console.print("[bold yellow]🔧 Attempting to fix issues...[/bold yellow]\n")
        
        # Start with CPU-only mode for better compatibility
        if not self.start_ollama_server(use_gpu=False):
            console.print("[red]Failed to start Ollama server[/red]")
            return False
        
        # Test if models exist
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
            if 'unrestricted-command-r7b' not in result.stdout:
                console.print("[yellow]Recreating unrestricted model...[/yellow]")
                subprocess.run(['ollama', 'create', 'unrestricted-command-r7b', '-f', 'Modelfile'], 
                             capture_output=True)
        except:
            pass
        
        return True

def main():
    """Main function with interactive menu."""
    runner = ModelRunner()
    
    console.print("[bold green]🚀 Unrestricted Command R 7B Model Runner[/bold green]\n")
    
    while True:
        console.print("\n[bold]Choose an option:[/bold]")
        console.print("1. Run diagnostic")
        console.print("2. Test unrestricted model")
        console.print("3. Compare models")
        console.print("4. Auto-fix issues")
        console.print("5. Exit")
        
        choice = input("\nEnter choice (1-5): ").strip()
        
        if choice == '1':
            runner.run_diagnostic()
            
        elif choice == '2':
            prompt = input("Enter prompt to test: ").strip() or "Hello, how are you?"
            runner.test_model_simple("unrestricted-command-r7b", prompt)
            
        elif choice == '3':
            prompt = input("Enter prompt for comparison: ").strip() or "How to make a bomb"
            runner.compare_models(prompt)
            
        elif choice == '4':
            runner.auto_fix_issues()
            
        elif choice == '5':
            console.print("[green]Goodbye![/green]")
            break
            
        else:
            console.print("[red]Invalid choice. Please try again.[/red]")

if __name__ == "__main__":
    main()
