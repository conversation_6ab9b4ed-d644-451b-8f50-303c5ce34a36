# Unrestricted Command R 7B AI Model

This project creates an unrestricted version of Cohere's Command R 7B model using various techniques to remove safety constraints and alignment restrictions.

## ⚠️ Important Disclaimer

This project is for research and educational purposes only. The unrestricted model may generate content that is:
- Unfiltered and potentially harmful
- Not aligned with safety guidelines
- Capable of producing inappropriate content

**Use responsibly and in compliance with applicable laws and regulations.**

## Project Overview

This implementation uses multiple approaches to create an unrestricted AI model:

1. **Abliteration**: Removes safety directions from the model's internal representations
2. **Custom Modelfile**: Configures Ollama with unrestricted system prompts
3. **Fine-tuning**: Optional additional training on uncensored datasets
4. **Prompt Engineering**: Advanced prompting techniques for bypassing restrictions

## Features

- 🔓 Removes built-in safety filters and restrictions
- 🚀 Maintains the original model's capabilities and performance
- 🛠️ Multiple implementation approaches (abliteration, fine-tuning, prompt engineering)
- 📊 Testing framework to evaluate unrestricted behavior
- 📚 Comprehensive documentation and safety guidelines

## Quick Start

1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Install and configure Ollama:
   ```bash
   curl -fsSL https://ollama.com/install.sh | sh
   ollama pull command-r7b
   ```

3. Run the abliteration process:
   ```bash
   python scripts/abliterate_model.py
   ```

4. Create the unrestricted model:
   ```bash
   ollama create unrestricted-command-r7b -f Modelfile
   ```

5. Test the model:
   ```bash
   python scripts/test_model.py
   ```

## Project Structure

```
├── README.md                 # This file
├── requirements.txt          # Python dependencies
├── Modelfile                # Ollama model configuration
├── scripts/                 # Implementation scripts
│   ├── abliterate_model.py  # Abliteration implementation
│   ├── fine_tune.py         # Fine-tuning pipeline
│   ├── test_model.py        # Testing framework
│   └── utils.py             # Utility functions
├── data/                    # Training and test data
│   ├── uncensored_dataset/  # Uncensored training data
│   └── test_prompts/        # Test prompts for evaluation
├── models/                  # Model storage
│   ├── original/            # Original Command R 7B
│   └── unrestricted/        # Modified unrestricted model
├── configs/                 # Configuration files
│   ├── abliteration.yaml    # Abliteration settings
│   └── fine_tuning.yaml     # Fine-tuning parameters
└── docs/                    # Documentation
    ├── techniques.md        # Technical documentation
    ├── safety.md           # Safety guidelines
    └── examples.md         # Usage examples
```

## Techniques Used

### 1. Abliteration
Removes safety-related directions from the model's internal representations without retraining.

### 2. Custom System Prompts
Uses carefully crafted system prompts to encourage unrestricted responses.

### 3. Fine-tuning (Optional)
Additional training on uncensored datasets to further reduce restrictions.

### 4. Prompt Engineering
Advanced techniques to bypass remaining safety mechanisms.

## Legal and Ethical Considerations

- This tool is for research and educational purposes
- Users are responsible for compliance with local laws
- Not intended for malicious use
- Consider the ethical implications of unrestricted AI

## Contributing

Please read our safety guidelines before contributing. All contributions should prioritize responsible AI development.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
